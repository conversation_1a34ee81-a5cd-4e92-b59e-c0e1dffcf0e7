<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .info { border-left: 4px solid #007bff; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #status { font-weight: bold; margin: 10px 0; }
        #logs { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>SSE Test Page</h1>
    <div id="status">Not connected</div>
    
    <button onclick="testSSE()">Test SSE Connection</button>
    <button onclick="testScraping()">Test Scraping SSE</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <h2>Logs:</h2>
    <div id="logs"></div>

    <script>
        let eventSource = null;
        
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logs.appendChild(logDiv);
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function testSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            addLog('Testing SSE connection to test server...', 'info');
            updateStatus('Connecting to test SSE...');
            
            eventSource = new EventSource('http://localhost:8001/test-sse');
            
            eventSource.onopen = function(event) {
                addLog('✅ SSE connection opened', 'success');
                updateStatus('Connected to test SSE');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 ${data.message}`, data.type || 'info');
                    
                    if (data.type === 'complete') {
                        eventSource.close();
                        updateStatus('Test completed');
                    }
                } catch (e) {
                    addLog(`❌ Error parsing message: ${e}`, 'error');
                }
            };
            
            eventSource.onerror = function(event) {
                addLog('❌ SSE connection error', 'error');
                updateStatus('SSE connection failed');
                console.error('SSE error:', event);
            };
        }
        
        function testScraping() {
            if (eventSource) {
                eventSource.close();
            }
            
            addLog('Testing scraping SSE connection...', 'info');
            updateStatus('Connecting to scraping SSE...');
            
            eventSource = new EventSource('http://localhost:8000/api/scrape/logs');
            
            eventSource.onopen = function(event) {
                addLog('✅ Scraping SSE connection opened', 'success');
                updateStatus('Connected to scraping SSE');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 ${data.message}`, data.type || 'info');
                    
                    if (data.type === 'complete') {
                        eventSource.close();
                        updateStatus('Scraping completed');
                    }
                } catch (e) {
                    addLog(`❌ Error parsing message: ${e}`, 'error');
                }
            };
            
            eventSource.onerror = function(event) {
                addLog('❌ Scraping SSE connection error', 'error');
                updateStatus('Scraping SSE connection failed');
                console.error('SSE error:', event);
            };
        }
    </script>
</body>
</html>
