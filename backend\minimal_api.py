#!/usr/bin/env python3
"""
Minimal API server to test SSE functionality
"""
from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import asyncio
import json
import logging
from datetime import datetime
from queue import Queue
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global queue for real-time logging
log_queue = Queue()
scraping_active = False

def add_log_message(message_type: str, message: str, data: dict = None):
    """Add a message to the log queue for real-time streaming"""
    log_data = {
        "type": message_type,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }
    if data:
        log_data.update(data)

    try:
        log_queue.put_nowait(json.dumps(log_data))
        logger.info(f"LOG_QUEUE: Added message [{message_type}]: {message} (Queue size: {log_queue.qsize()})")
    except Exception as e:
        logger.error(f"Failed to add log message: {e}")

# Create the FastAPI app
app = FastAPI(title="Minimal JoMaDe API")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Minimal JoMaDe API is running", "timestamp": datetime.now().isoformat()}

@app.get("/api/scrape/logs")
async def scrape_logs():
    """Stream real-time scraping logs to the frontend"""
    logger.info("SSE: New client connected to scrape logs stream")

    async def event_stream():
        client_id = id(event_stream)
        logger.info(f"SSE: Starting event stream for client {client_id}")

        # Send initial connection message
        initial_message = json.dumps({
            "type": "connected",
            "message": "Connected to real-time logging",
            "timestamp": datetime.now().isoformat()
        })
        yield f"data: {initial_message}\n\n"

        heartbeat_counter = 0

        while True:
            try:
                # Check if there are messages in the queue
                if not log_queue.empty():
                    message = log_queue.get_nowait()
                    logger.info(f"SSE: Sending message to client {client_id}: {message}")
                    yield f"data: {message}\n\n"
                else:
                    # Send heartbeat every 10 iterations (1 second) to keep connection alive
                    heartbeat_counter += 1
                    if heartbeat_counter >= 10:
                        heartbeat = json.dumps({
                            "type": "heartbeat",
                            "timestamp": datetime.now().isoformat(),
                            "scraping_active": scraping_active,
                            "queue_size": log_queue.qsize()
                        })
                        yield f"data: {heartbeat}\n\n"
                        heartbeat_counter = 0

                await asyncio.sleep(0.1)  # Small delay to prevent overwhelming

                # Stop streaming if scraping is not active and queue is empty
                if not scraping_active and log_queue.empty():
                    completion_message = json.dumps({
                        "type": "complete",
                        "message": "Scraping completed",
                        "timestamp": datetime.now().isoformat()
                    })
                    yield f"data: {completion_message}\n\n"
                    logger.info(f"SSE: Stream completed for client {client_id}")
                    break

            except Exception as e:
                logger.error(f"SSE: Error in event stream for client {client_id}: {e}")
                error_message = json.dumps({
                    "type": "error",
                    "message": f"Stream error: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })
                yield f"data: {error_message}\n\n"
                break

        logger.info(f"SSE: Event stream ended for client {client_id}")

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        }
    )

@app.post("/test/sse-logs")
async def test_sse_logs():
    """Test endpoint to send sample messages to SSE stream"""
    global scraping_active

    # Temporarily activate scraping to test SSE
    scraping_active = True

    # Send test messages
    add_log_message("info", "🧪 Testing SSE connection...")
    add_log_message("info", "📋 Found 5 test URLs to scrape")
    add_log_message("info", "🔧 Initializing test scraper...")
    add_log_message("success", "✅ Test message 1: Success!")
    add_log_message("warning", "⚠️ Test message 2: Warning!")
    add_log_message("error", "❌ Test message 3: Error!")
    add_log_message("complete", "🎯 SSE test completed")

    # Reset scraping active after a delay
    import threading
    def reset_scraping():
        import time
        time.sleep(2)
        global scraping_active
        scraping_active = False
    
    threading.Thread(target=reset_scraping).start()

    return {"success": True, "message": "Test messages sent to SSE stream"}

if __name__ == "__main__":
    print("🚀 Starting Minimal JoMaDe API server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
