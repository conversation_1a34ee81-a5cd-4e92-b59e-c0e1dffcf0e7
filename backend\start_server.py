#!/usr/bin/env python3
"""
Simple script to start the JoMaDe API server
"""
import uvicorn
import os
import sys

def main():
    print("🚀 Starting JoMaDe API server...")
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"🐍 Python version: {sys.version}")
    
    try:
        # Import the app to check for any issues
        from api import app
        print("✅ API module imported successfully")
        
        # Start the server
        print("🌐 Starting server on http://0.0.0.0:8000")
        uvicorn.run(
            "api:app", 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
