#!/usr/bin/env python3
"""
Test script for toggle all URLs functionality using Python requests
"""

import requests
import json

def test_toggle_endpoint():
    base_url = "http://localhost:8001"
    endpoint = f"{base_url}/api/urls/toggle-all"
    
    print("Testing Toggle All URLs Endpoint")
    print("=" * 50)
    
    # Test 1: Enable all URLs
    print("\n1. Testing Enable All URLs...")
    enable_data = {"include": True}
    
    try:
        response = requests.put(
            endpoint,
            json=enable_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'No message')}")
            print(f"Updated Count: {result.get('updated_count', 'Unknown')}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)
    
    # Test 2: Disable all URLs
    print("\n2. Testing Disable All URLs...")
    disable_data = {"include": False}
    
    try:
        response = requests.put(
            endpoint,
            json=disable_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'No message')}")
            print(f"Updated Count: {result.get('updated_count', 'Unknown')}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_toggle_endpoint()
