<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JoMaDe - Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .nav {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border-radius: 25px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-dot.error {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .input-group input:focus, .input-group textarea:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .url-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .url-item .prefix {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        
        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }

        .cache-status {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .cache-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .cache-stat:last-child {
            border-bottom: none;
        }

        .cache-stat-label {
            font-weight: 600;
            color: #495057;
        }

        .cache-stat-value {
            color: #667eea;
            font-weight: bold;
        }

        .cache-stat-value.fresh {
            color: #28a745;
        }

        .cache-stat-value.expired {
            color: #dc3545;
        }

        .status-loading {
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>JoMaDe</h1>
            <p>Settings & Configuration</p>
        </div>
        
        <div class="nav">
            <a href="index.html">Dashboard</a>
            <a href="settings.html" class="active">Settings</a>
        </div>
        
        <!-- System Status -->
        <div class="card">
            <h2>System Status</h2>
            <div class="status">
                <div class="status-dot" id="backend-status"></div>
                <span id="backend-text">Checking backend connection...</span>
            </div>
            <button class="btn" onclick="checkBackendHealth()">Refresh Status</button>
        </div>
        
        <!-- Job URL Management -->
        <div class="card">
            <h2>Job URL Management</h2>
            <div class="info-message">
                <strong>URL Prefixes:</strong> Each URL is automatically assigned a unique three-letter prefix (AAA, AAB, AAC, etc.) for identification.
            </div>
            
            <div class="input-group">
                <label for="new-url">Add New Job URL:</label>
                <input type="url" id="new-url" placeholder="https://example.com/jobs">
            </div>
            <button class="btn" onclick="addNewUrl()">Add URL</button>
            
            <h3 style="margin-top: 30px; margin-bottom: 15px;">Current URLs:</h3>

            <!-- Toggle All Controls -->
            <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                    <span style="font-weight: 500; color: #495057;">Bulk Actions:</span>
                    <button class="btn" onclick="toggleAllUrls(true)"
                            style="background: #28a745; color: white; padding: 6px 12px; font-size: 0.9rem;">
                        ✅ Enable All
                    </button>
                    <button class="btn" onclick="toggleAllUrls(false)"
                            style="background: #6c757d; color: white; padding: 6px 12px; font-size: 0.9rem;">
                        ⏸️ Disable All
                    </button>
                    <span id="toggle-status" style="font-size: 0.85em; color: #6c757d; margin-left: 10px;"></span>
                </div>
            </div>

            <div id="url-list">
                <p>Loading URLs...</p>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn" onclick="loadUrls()">Refresh URLs</button>
                <button class="btn btn-secondary" onclick="exportUrls()">Export URLs</button>
                <button class="btn btn-danger" onclick="clearAllUrls()">Clear All URLs</button>
            </div>
            <div id="url-message"></div>
        </div>

        <!-- Firecrawl Cache Management -->
        <div class="card">
            <h2>Firecrawl Cache Management</h2>
            <div class="info-message">
                <strong>Smart Caching:</strong> JoMaDe uses Firecrawl's session management as the single source of truth.
                Recent crawl results are automatically reused to minimize API costs and improve performance.
            </div>

            <div class="cache-status" id="cache-status">
                <div class="status-loading">Loading cache status...</div>
            </div>

            <div class="button-group">
                <button class="btn" onclick="refreshCacheStatus()">Refresh Status</button>
                <button class="btn btn-danger" onclick="clearCache()">Clear Cache</button>
            </div>
            <div id="cache-message"></div>
        </div>

        <!-- User Settings -->
        <div class="card">
            <h2>User Settings</h2>
            
            <div class="input-group">
                <label for="cv-summary">CV Summary:</label>
                <textarea id="cv-summary" rows="6" placeholder="Enter your CV summary here..."></textarea>
            </div>
            <button class="btn" onclick="saveCvSummary()">Save CV Summary</button>
            <div id="cv-message"></div>
        </div>
        
        <!-- API Settings -->
        <div class="card">
            <h2>API Configuration</h2>
            <div class="info-message">
                <strong>Note:</strong> API keys are stored securely and masked for security.
            </div>
            
            <div class="input-group">
                <label for="firecrawl-api">Firecrawl API Key:</label>
                <input type="password" id="firecrawl-api" placeholder="Enter Firecrawl API key">
            </div>
            
            <div class="input-group">
                <label for="kadoa-api">Kadoa API Key:</label>
                <input type="password" id="kadoa-api" placeholder="Enter Kadoa API key">
            </div>
            
            <div class="input-group">
                <label for="openai-api">OpenAI API Key:</label>
                <input type="password" id="openai-api" placeholder="Enter OpenAI API key">
            </div>
            
            <button class="btn" onclick="saveApiKeys()">Save API Keys</button>
            <div id="api-message"></div>
        </div>
        
        <!-- System Settings -->
        <div class="card">
            <h2>System Settings</h2>
            
            <div class="input-group">
                <label for="scraping-method">Preferred Scraping Method:</label>
                <select id="scraping-method">
                    <option value="firecrawl">Firecrawl API</option>
                    <option value="kadoa">Kadoa API</option>
                    <option value="manual">Manual URLs</option>
                </select>
            </div>
            
            <div class="input-group">
                <label for="max-jobs">Maximum Jobs per Source:</label>
                <input type="number" id="max-jobs" value="10" min="1" max="100">
            </div>
            
            <div class="input-group">
                <label for="auto-refresh">Auto-refresh Interval (minutes):</label>
                <input type="number" id="auto-refresh" value="60" min="5" max="1440">
            </div>
            
            <button class="btn" onclick="saveSystemSettings()">Save System Settings</button>
            <div id="system-message"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Check backend health
        async function checkBackendHealth() {
            const statusDot = document.getElementById('backend-status');
            const statusText = document.getElementById('backend-text');
            
            try {
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Backend is running ✓';
                } else {
                    throw new Error('Backend responded with error');
                }
            } catch (error) {
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Backend is not responding ✗';
            }
        }
        
        // Load URLs
        async function loadUrls() {
            console.log('🔄 loadUrls() called');
            try {
                const response = await fetch(`${API_BASE}/job-urls`);
                console.log('📡 loadUrls response status:', response.status);
                if (response.ok) {
                    const data = await response.json();
                    const urls = data.urls || [];
                    console.log('📊 loadUrls received:', urls.length, 'URLs');
                    console.log('🔗 URLs:', urls);
                    displayUrls(urls);
                    console.log('✅ displayUrls() called with', urls.length, 'URLs');
                } else {
                    throw new Error('Failed to load URLs');
                }
            } catch (error) {
                console.error('❌ loadUrls error:', error);
                showMessage('url-message', 'Error loading URLs: ' + error.message, 'error');
            }
        }
        
        // Display URLs with enhanced management
        async function displayUrls(urls) {
            console.log('🖼️ displayUrls() called with', urls.length, 'URLs');
            const container = document.getElementById('url-list');
            console.log('📦 Container element:', container ? 'found' : 'NOT FOUND');

            if (urls.length === 0) {
                console.log('📭 No URLs to display');
                container.innerHTML = '<p>No URLs configured yet.</p>';
                return;
            }

            // Get URL data with include_in_scraping status
            let urlData = [];
            try {
                const response = await fetch(`${API_BASE}/job-urls`);
                if (response.ok) {
                    const data = await response.json();
                    urlData = data.url_data || [];
                }
            } catch (error) {
                console.error('Error loading URL data:', error);
            }

            // Get source statistics
            let sourceStats = {};
            try {
                const statsResponse = await fetch(`${API_BASE}/api/sources/stats`);
                if (statsResponse.ok) {
                    const statsData = await statsResponse.json();
                    sourceStats = statsData.source_stats || {};
                }
            } catch (error) {
                console.error('Error loading source stats:', error);
            }

            const urlsHtml = urls.map((url, index) => {
                // Find URL data for this URL
                const urlInfo = urlData.find(u => u.url === url) || {};
                const prefix = urlInfo.prefix || generatePrefix(index);
                const includeInScraping = urlInfo.include_in_scraping !== false;
                const lastScraped = urlInfo.last_scraped;

                // Get statistics for this URL
                const stats = sourceStats[url] || { scraped_jobs: 0 };
                const jobCount = stats.scraped_jobs || 0;
                const lastScrapedText = lastScraped ?
                    new Date(lastScraped).toLocaleDateString() :
                    'Never';

                // Status styling
                const statusClass = includeInScraping ? 'url-item' : 'url-item disabled';
                const statusIcon = includeInScraping ? '✅' : '⏸️';

                return `
                    <div class="${statusClass}" style="${!includeInScraping ? 'opacity: 0.7; background: #fff3cd;' : ''}">
                        <div style="display: flex; align-items: center; gap: 10px; flex: 1;">
                            <span style="font-size: 1.2em;">${statusIcon}</span>
                            <input type="checkbox"
                                   id="include-${prefix}"
                                   ${includeInScraping ? 'checked' : ''}
                                   onchange="updateUrlIncludeStatus('${prefix}', this.checked)"
                                   title="Include in scraping operations">
                            <label for="include-${prefix}" style="cursor: pointer; margin-right: 10px;">
                                <span class="prefix">${prefix}</span>
                            </label>
                            <div style="flex: 1; min-width: 0;">
                                <div style="word-break: break-all; margin-bottom: 4px;">${url}</div>
                                <div style="font-size: 0.8em; color: #666;">
                                    ${jobCount} jobs • Last scraped: ${lastScrapedText}
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 5px; align-items: center;">
                            <button class="btn btn-danger" onclick="removeUrl(${index})"
                                    style="padding: 5px 10px; font-size: 0.8rem;">
                                Remove
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            console.log('🎨 Setting innerHTML with', urls.length, 'URL items');
            container.innerHTML = urlsHtml;
            console.log('✅ displayUrls() completed');
        }
        
        // Generate prefix (AAA, AAB, AAC, etc.)
        function generatePrefix(index) {
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let result = '';
            let num = index;
            
            for (let i = 0; i < 3; i++) {
                result = letters[num % 26] + result;
                num = Math.floor(num / 26);
            }
            
            return result;
        }
        
        // Add new URL
        async function addNewUrl() {
            const urlInput = document.getElementById('new-url');
            const url = urlInput.value.trim();
            
            if (!url) {
                showMessage('url-message', 'Please enter a URL', 'error');
                return;
            }
            
            try {
                // Get current URLs
                const response = await fetch(`${API_BASE}/job-urls`);
                const data = await response.json();
                const currentUrls = data.urls || [];
                
                // Add new URL
                currentUrls.push(url);
                
                // Save updated URLs
                const saveResponse = await fetch(`${API_BASE}/job-urls`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ urls: currentUrls })
                });
                
                if (saveResponse.ok) {
                    urlInput.value = '';
                    showMessage('url-message', 'URL added successfully!', 'success');
                    loadUrls();
                } else {
                    throw new Error('Failed to save URL');
                }
            } catch (error) {
                showMessage('url-message', 'Error adding URL: ' + error.message, 'error');
            }
        }
        
        // Remove URL
        async function removeUrl(index) {
            console.log('🗑️ removeUrl called with index:', index);
            try {
                // Get current URLs
                console.log('📡 Fetching current URLs...');
                const response = await fetch(`${API_BASE}/job-urls`);
                const data = await response.json();
                const currentUrls = data.urls || [];
                console.log('📊 Current URLs:', currentUrls.length, 'URLs found');
                console.log('🎯 Removing URL at index', index, ':', currentUrls[index]);

                // Remove URL at index
                currentUrls.splice(index, 1);
                console.log('✂️ After removal:', currentUrls.length, 'URLs remaining');

                // Save updated URLs
                console.log('💾 Saving updated URLs...');
                const saveResponse = await fetch(`${API_BASE}/job-urls`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ urls: currentUrls, replace_all: true })
                });

                console.log('📡 Save response status:', saveResponse.status);
                if (saveResponse.ok) {
                    console.log('✅ URL removed successfully!');
                    showMessage('url-message', 'URL removed successfully!', 'success');
                    loadUrls();
                } else {
                    const errorText = await saveResponse.text();
                    console.error('❌ Save failed:', saveResponse.status, errorText);
                    throw new Error('Failed to remove URL');
                }
            } catch (error) {
                console.error('❌ Remove URL error:', error);
                showMessage('url-message', 'Error removing URL: ' + error.message, 'error');
            }
        }
        
        // Clear all URLs
        async function clearAllUrls() {
            if (!confirm('Are you sure you want to remove all URLs?')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/job-urls`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ urls: [] })
                });
                
                if (response.ok) {
                    showMessage('url-message', 'All URLs cleared successfully!', 'success');
                    loadUrls();
                } else {
                    throw new Error('Failed to clear URLs');
                }
            } catch (error) {
                showMessage('url-message', 'Error clearing URLs: ' + error.message, 'error');
            }
        }
        
        // Export URLs
        function exportUrls() {
            fetch(`${API_BASE}/job-urls`)
                .then(response => response.json())
                .then(data => {
                    const urls = data.urls || [];
                    const blob = new Blob([urls.join('\n')], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'job_urls.txt';
                    a.click();
                    URL.revokeObjectURL(url);
                    showMessage('url-message', 'URLs exported successfully!', 'success');
                })
                .catch(error => {
                    showMessage('url-message', 'Error exporting URLs: ' + error.message, 'error');
                });
        }

        // Update URL include status
        async function updateUrlIncludeStatus(prefix, include) {
            try {
                const response = await fetch(`${API_BASE}/api/urls/${prefix}/include?include=${include}`, {
                    method: 'PUT'
                });

                if (response.ok) {
                    console.log(`Updated ${prefix} include status to ${include}`);
                    showMessage('url-message', `Updated ${prefix} scraping status`, 'success');
                    // Refresh the display to show updated status
                    setTimeout(() => loadUrls(), 500);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Failed to update URL status');
                }
            } catch (error) {
                console.error('Error updating URL status:', error);
                showMessage('url-message', 'Error updating URL: ' + error.message, 'error');
                // Revert checkbox state
                document.getElementById(`include-${prefix}`).checked = !include;
            }
        }

        // Toggle all URLs include status
        async function toggleAllUrls(include) {
            const statusElement = document.getElementById('toggle-status');
            const action = include ? 'Enabling' : 'Disabling';

            try {
                // Show loading status
                statusElement.textContent = `${action} all URLs...`;
                statusElement.style.color = '#007bff';

                const response = await fetch(`${API_BASE}/api/urls/toggle-all`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ include: include })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log(`Toggle all URLs result:`, data);

                    // Show success message
                    const actionPast = include ? 'enabled' : 'disabled';
                    showMessage('url-message', `Successfully ${actionPast} ${data.updated_count} URLs for scraping`, 'success');

                    // Update status
                    statusElement.textContent = `${data.updated_count} URLs ${actionPast}`;
                    statusElement.style.color = '#28a745';

                    // Refresh the display to show updated checkboxes
                    setTimeout(() => {
                        loadUrls();
                        // Clear status after refresh
                        setTimeout(() => {
                            statusElement.textContent = '';
                        }, 2000);
                    }, 500);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Failed to toggle all URLs');
                }
            } catch (error) {
                console.error('Error toggling all URLs:', error);
                showMessage('url-message', 'Error toggling all URLs: ' + error.message, 'error');

                // Show error status
                statusElement.textContent = 'Error occurred';
                statusElement.style.color = '#dc3545';

                // Clear error status after a few seconds
                setTimeout(() => {
                    statusElement.textContent = '';
                }, 3000);
            }
        }

        // Save CV summary
        async function saveCvSummary() {
            const summary = document.getElementById('cv-summary').value;
            
            try {
                const response = await fetch(`${API_BASE}/cv-summary`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ summary })
                });
                
                if (response.ok) {
                    showMessage('cv-message', 'CV summary saved successfully!', 'success');
                } else {
                    throw new Error('Failed to save CV summary');
                }
            } catch (error) {
                showMessage('cv-message', 'Error saving CV summary: ' + error.message, 'error');
            }
        }
        
        // Load CV summary
        async function loadCvSummary() {
            try {
                const response = await fetch(`${API_BASE}/cv-summary`);
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('cv-summary').value = data.summary || '';
                }
            } catch (error) {
                console.error('Error loading CV summary:', error);
            }
        }
        
        // Save API keys (mock implementation)
        function saveApiKeys() {
            showMessage('api-message', 'API keys saved successfully! (Note: This is a mock implementation)', 'success');
        }
        
        // Save system settings (mock implementation)
        function saveSystemSettings() {
            showMessage('system-message', 'System settings saved successfully! (Note: This is a mock implementation)', 'success');
        }

        // Cache management functions
        async function refreshCacheStatus() {
            const statusContainer = document.getElementById('cache-status');
            statusContainer.innerHTML = '<div class="status-loading">Loading cache status...</div>';

            try {
                const response = await fetch(`${API_BASE}/api/scrape/cache/status`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        displayCacheStatus(data.cache_status);
                    } else {
                        statusContainer.innerHTML = `<div class="error-message">Error: ${data.error}</div>`;
                    }
                } else {
                    throw new Error('Failed to fetch cache status');
                }
            } catch (error) {
                console.error('Error fetching cache status:', error);
                statusContainer.innerHTML = '<div class="error-message">Failed to load cache status</div>';
            }
        }

        function displayCacheStatus(cacheStatus) {
            const statusContainer = document.getElementById('cache-status');

            const html = `
                <div class="cache-stat">
                    <span class="cache-stat-label">Total Cached URLs:</span>
                    <span class="cache-stat-value">${cacheStatus.total_cached_urls}</span>
                </div>
                <div class="cache-stat">
                    <span class="cache-stat-label">Fresh Cache Entries:</span>
                    <span class="cache-stat-value fresh">${cacheStatus.fresh_cache_count}</span>
                </div>
                <div class="cache-stat">
                    <span class="cache-stat-label">Expired Cache Entries:</span>
                    <span class="cache-stat-value expired">${cacheStatus.expired_cache_count}</span>
                </div>
                <div class="cache-stat">
                    <span class="cache-stat-label">Cache Duration:</span>
                    <span class="cache-stat-value">${cacheStatus.cache_hours} hours</span>
                </div>
                <div class="cache-stat">
                    <span class="cache-stat-label">Cache File Status:</span>
                    <span class="cache-stat-value ${cacheStatus.cache_file_exists ? 'fresh' : 'expired'}">
                        ${cacheStatus.cache_file_exists ? 'Exists' : 'Not Found'}
                    </span>
                </div>
            `;

            statusContainer.innerHTML = html;
        }

        async function clearCache() {
            if (!confirm('Are you sure you want to clear the Firecrawl cache? This will force fresh crawls for all URLs.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/scrape/cache`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showMessage('cache-message', data.message, 'success');
                        refreshCacheStatus(); // Refresh the display
                    } else {
                        showMessage('cache-message', `Error: ${data.error}`, 'error');
                    }
                } else {
                    throw new Error('Failed to clear cache');
                }
            } catch (error) {
                console.error('Error clearing cache:', error);
                showMessage('cache-message', 'Error clearing cache: ' + error.message, 'error');
            }
        }

        // Show message helper
        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}-message">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkBackendHealth();
            loadUrls();
            loadCvSummary();
            refreshCacheStatus();
        });
    </script>
</body>
</html>
