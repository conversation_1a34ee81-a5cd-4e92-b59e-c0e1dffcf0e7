<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Debug Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .log {
            background: #2d2d2d;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #005a9e; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>SSE Debug Test</h1>
    
    <div class="status" id="status">Ready to test</div>
    
    <button onclick="testScraping()">Test Scraping SSE</button>
    <button onclick="testSimpleSSE()">Test Simple SSE</button>
    <button onclick="clearLog()">Clear Log</button>
    <button onclick="closeConnection()">Close Connection</button>
    
    <div class="log" id="log"></div>
    
    <script>
        let eventSource = null;
        
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${timestamp}] ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function testScraping() {
            if (eventSource) {
                eventSource.close();
            }

            addLog('Testing scraping SSE connection...', 'info');
            updateStatus('Connecting to scraping SSE...');

            eventSource = new EventSource('http://localhost:8000/api/scrape/logs');

            eventSource.onopen = function(event) {
                addLog('✅ Scraping SSE connection opened', 'success');
                updateStatus('Connected to scraping SSE');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 ${data.message}`, data.type || 'info');

                    if (data.type === 'complete') {
                        eventSource.close();
                        updateStatus('Scraping completed');
                    }
                } catch (e) {
                    addLog(`❌ Error parsing message: ${e}`, 'error');
                }
            };

            eventSource.onerror = function(event) {
                addLog('❌ Scraping SSE connection error', 'error');
                updateStatus('Scraping SSE connection failed', 'error');
                console.error('SSE Error:', event);
                console.log('EventSource readyState:', eventSource.readyState);
                console.log('EventSource url:', eventSource.url);
            };
        }

        function testSimpleSSE() {
            if (eventSource) {
                eventSource.close();
            }

            addLog('Testing simple SSE connection...', 'info');
            updateStatus('Connecting to simple SSE...');

            eventSource = new EventSource('http://localhost:8000/test/simple-sse');

            eventSource.onopen = function(event) {
                addLog('✅ Simple SSE connection opened', 'success');
                updateStatus('Connected to simple SSE');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 ${data.message}`, data.type || 'info');

                    if (data.type === 'complete') {
                        eventSource.close();
                        updateStatus('Simple SSE completed');
                    }
                } catch (e) {
                    addLog(`❌ Error parsing message: ${e}`, 'error');
                }
            };

            eventSource.onerror = function(event) {
                addLog('❌ Simple SSE connection error', 'error');
                updateStatus('Simple SSE connection failed', 'error');
                console.error('SSE Error:', event);
                console.log('EventSource readyState:', eventSource.readyState);
                console.log('EventSource url:', eventSource.url);
            };
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function closeConnection() {
            if (eventSource) {
                eventSource.close();
                addLog('Connection closed manually', 'warning');
                updateStatus('Connection closed');
            }
        }
        
        // Test basic connectivity
        async function testConnectivity() {
            try {
                addLog('Testing basic connectivity to backend...', 'info');
                const response = await fetch('http://localhost:8000/');
                if (response.ok) {
                    const data = await response.json();
                    addLog(`✅ Backend is reachable: ${data.message}`, 'success');
                } else {
                    addLog(`❌ Backend responded with status: ${response.status}`, 'error');
                }
            } catch (e) {
                addLog(`❌ Backend connectivity error: ${e.message}`, 'error');
            }
        }
        
        // Run connectivity test on load
        window.onload = function() {
            testConnectivity();
        };
    </script>
</body>
</html>
