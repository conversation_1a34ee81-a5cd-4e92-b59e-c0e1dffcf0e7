# Test script for toggle all URLs functionality

# Test enabling all URLs
Write-Host "Testing Enable All URLs..." -ForegroundColor Green
$enableBody = @{
    include = $true
} | ConvertTo-Json

try {
    $enableResult = Invoke-RestMethod -Uri 'http://localhost:8001/api/urls/toggle-all' -Method PUT -ContentType 'application/json' -Body $enableBody
    Write-Host "Enable All Result:" -ForegroundColor Green
    $enableResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Enable All Error:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

Write-Host "`n" + "="*50 + "`n"

# Test disabling all URLs
Write-Host "Testing Disable All URLs..." -ForegroundColor Yellow
$disableBody = @{
    include = $false
} | ConvertTo-Json

try {
    $disableResult = Invoke-RestMethod -Uri 'http://localhost:8001/api/urls/toggle-all' -Method PUT -ContentType 'application/json' -Body $disableBody
    Write-Host "Disable All Result:" -ForegroundColor Yellow
    $disableResult | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Disable All Error:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
