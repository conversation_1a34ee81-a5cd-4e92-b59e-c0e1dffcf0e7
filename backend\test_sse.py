#!/usr/bin/env python3
"""
Simple test server to verify SSE functionality
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import asyncio
import json
from datetime import datetime
import uvicorn

app = FastAPI(title="SSE Test Server")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "SSE Test Server is running"}

@app.get("/test-sse")
async def test_sse():
    """Test SSE endpoint"""
    async def event_stream():
        # Send initial connection message
        initial_message = json.dumps({
            "type": "connected",
            "message": "Connected to test SSE stream",
            "timestamp": datetime.now().isoformat()
        })
        yield f"data: {initial_message}\n\n"
        
        # Send test messages
        for i in range(5):
            await asyncio.sleep(1)
            message = json.dumps({
                "type": "info",
                "message": f"Test message {i+1}",
                "timestamp": datetime.now().isoformat(),
                "count": i+1
            })
            yield f"data: {message}\n\n"
        
        # Send completion message
        completion_message = json.dumps({
            "type": "complete",
            "message": "Test completed",
            "timestamp": datetime.now().isoformat()
        })
        yield f"data: {completion_message}\n\n"
    
    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        }
    )

if __name__ == "__main__":
    print("🧪 Starting SSE Test Server on http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
